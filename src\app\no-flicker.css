/* Prevent flicker during page load */
.no-flicker-skeleton {
  display: block;
}

/* Hide real content until JS loads */
.js-content {
  display: none;
}

/* Once JS loads, this class will be added to body */
body.js-loaded .no-flicker-skeleton {
  display: none;
}

body.js-loaded .js-content {
  display: block;
}

/* Static skeleton styles */
.skeleton-bg {
  background-color: #f3f4f6;
}

.skeleton-item {
  background-color: #e5e7eb;
  border-radius: 0.375rem;
  animation: none !important;
}

/* Prevent any animations until page is fully loaded */
body:not(.js-loaded) * {
  animation: none !important;
  transition: none !important;
}
